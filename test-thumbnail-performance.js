#!/usr/bin/env node

/**
 * Performance test for optimized thumbnail generation
 * This script tests the speed improvements of the Sharp optimizations
 */

import sharp from 'sharp';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simulate the optimized thumbnail generation
async function generateOptimizedThumbnail(filePath, maxHeight = 256) {
  // Configure Sharp for maximum performance
  sharp.cache(false);
  sharp.simd(true);
  sharp.concurrency(1);

  // Use optimized JPEG processing for speed
  const jpegBuffer = await sharp(filePath)
    .resize(maxHeight, maxHeight, {
      fit: 'inside',
      withoutEnlargement: true,
      kernel: 'nearest' // Fastest resize algorithm
    })
    .jpeg({ 
      quality: 80,
      progressive: false,
      mozjpeg: false // Disable mozjpeg for speed
    })
    .toBuffer();

  return jpegBuffer;
}

// Simulate the old PNG-based thumbnail generation
async function generateOldThumbnail(filePath, maxHeight = 256) {
  const metadata = await sharp(filePath).metadata();
  const originalWidth = metadata.width || 0;
  const originalHeight = metadata.height || 0;
  
  let newWidth = originalWidth;
  let newHeight = originalHeight;

  if (newHeight > maxHeight) {
    const aspectRatio = newWidth / newHeight;
    newHeight = maxHeight;
    newWidth = Math.round(maxHeight * aspectRatio);
  }

  // Old PNG processing (slower)
  const pngBuffer = await sharp(filePath)
    .resize(newWidth, newHeight, {
      fit: 'inside',
      withoutEnlargement: true
    })
    .png()
    .toBuffer();

  return pngBuffer;
}

async function performanceTest() {
  console.log('🚀 Testing thumbnail generation performance improvements...');
  
  const testImagePath = path.join(__dirname, 'test-image.jpg');
  
  if (!fs.existsSync(testImagePath)) {
    console.log('❌ Test image not found. Please provide a test image at:', testImagePath);
    return;
  }

  const iterations = 10;
  console.log(`Running ${iterations} iterations for each method...\n`);

  // Test old method (PNG)
  console.log('📊 Testing OLD method (PNG with metadata calculation):');
  const oldStartTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    await generateOldThumbnail(testImagePath);
  }
  
  const oldEndTime = Date.now();
  const oldTotalTime = oldEndTime - oldStartTime;
  const oldAvgTime = oldTotalTime / iterations;
  
  console.log(`Total time: ${oldTotalTime}ms`);
  console.log(`Average time per thumbnail: ${oldAvgTime.toFixed(2)}ms\n`);

  // Test new optimized method (JPEG)
  console.log('⚡ Testing NEW optimized method (JPEG with performance settings):');
  const newStartTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    await generateOptimizedThumbnail(testImagePath);
  }
  
  const newEndTime = Date.now();
  const newTotalTime = newEndTime - newStartTime;
  const newAvgTime = newTotalTime / iterations;
  
  console.log(`Total time: ${newTotalTime}ms`);
  console.log(`Average time per thumbnail: ${newAvgTime.toFixed(2)}ms\n`);

  // Calculate improvement
  const speedImprovement = ((oldTotalTime - newTotalTime) / oldTotalTime) * 100;
  const speedMultiplier = oldTotalTime / newTotalTime;
  
  console.log('📈 PERFORMANCE RESULTS:');
  console.log(`Speed improvement: ${speedImprovement.toFixed(1)}%`);
  console.log(`Speed multiplier: ${speedMultiplier.toFixed(2)}x faster`);
  console.log(`Time saved per thumbnail: ${(oldAvgTime - newAvgTime).toFixed(2)}ms`);

  // Test file sizes
  console.log('\n📦 FILE SIZE COMPARISON:');
  const oldBuffer = await generateOldThumbnail(testImagePath);
  const newBuffer = await generateOptimizedThumbnail(testImagePath);
  
  console.log(`Old PNG size: ${oldBuffer.length} bytes`);
  console.log(`New JPEG size: ${newBuffer.length} bytes`);
  
  const sizeReduction = ((oldBuffer.length - newBuffer.length) / oldBuffer.length) * 100;
  console.log(`Size reduction: ${sizeReduction.toFixed(1)}%`);
  
  console.log('\n🎉 Optimization test completed!');
}

// Run the test
performanceTest().catch(console.error);
